-- Auction Winner Failsaf<PERSON> Trigger
-- Automatically creates auction winners when auction status changes to CLOSED
-- This serves as a backup mechanism in case the Edge Function fails

-- Create function to automatically select auction winners
CREATE OR REPLACE FUNCTION public.auto_select_auction_winners()
RETURNS TRIGGER AS $$
DECLARE
    winner_count INTEGER;
    bid_record RECORD;
    current_position INTEGER := 1;
BEGIN
    -- Only process when status changes to CLOSED and there are no existing winners
    IF NEW.status = 'CLOSED'::auction_state AND OLD.status != 'CLOSED'::auction_state THEN
        
        -- Check if winners already exist (Edge Function may have already created them)
        SELECT COUNT(*) INTO winner_count
        FROM public.auction_winner
        WHERE auction_id = NEW.id;
        
        -- Only create winners if none exist
        IF winner_count = 0 THEN
            RAISE NOTICE 'Auto-selecting winners for auction % (failsafe trigger)', NEW.id;
            
            -- Insert top 3 bids as winners, ordered by amount (lowest first)
            FOR bid_record IN
                SELECT b.id as bid_id, b.broker_id, b.amount
                FROM public.bid b
                WHERE b.auction_id = NEW.id
                ORDER BY b.amount ASC
                LIMIT 3
            LOOP
                INSERT INTO public.auction_winner (
                    auction_id,
                    broker_id,
                    bid_id,
                    position,
                    selected_at,
                    contact_data_revealed_at
                ) VALUES (
                    NEW.id,
                    bid_record.broker_id,
                    bid_record.bid_id,
                    current_position,
                    NOW(),
                    NULL -- Initially null, set when account holder contacts broker
                );
                
                RAISE NOTICE 'Created winner position % for auction % (bid: %, amount: %)', 
                    current_position, NEW.id, bid_record.bid_id, bid_record.amount;
                
                current_position := current_position + 1;
            END LOOP;
            
            RAISE NOTICE 'Failsafe trigger created % winners for auction %', current_position - 1, NEW.id;
        ELSE
            RAISE NOTICE 'Auction % already has % winners, skipping failsafe trigger', NEW.id, winner_count;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger that fires after auction status update
DROP TRIGGER IF EXISTS trigger_auto_select_winners ON public.auction;
CREATE TRIGGER trigger_auto_select_winners
    AFTER UPDATE ON public.auction
    FOR EACH ROW
    EXECUTE FUNCTION public.auto_select_auction_winners();

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.auto_select_auction_winners() TO authenticated;
GRANT EXECUTE ON FUNCTION public.auto_select_auction_winners() TO service_role;

-- Add comments for documentation
COMMENT ON FUNCTION public.auto_select_auction_winners() IS 
'Failsafe function that automatically creates auction winners when auction status changes to CLOSED. Only activates if no winners already exist (Edge Function takes priority). Selects top 3 bids by lowest amount.';

COMMENT ON TRIGGER trigger_auto_select_winners ON public.auction IS 
'Failsafe trigger that ensures auction winners are created even if the Edge Function fails. Activates only when no existing winners are found.';

-- Log successful creation
DO $$
BEGIN
    RAISE NOTICE 'Auction winner failsafe trigger created successfully. This will automatically create winners for closed auctions if the Edge Function fails.';
END $$;
