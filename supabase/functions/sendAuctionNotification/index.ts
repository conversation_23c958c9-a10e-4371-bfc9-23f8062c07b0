/**
 * Supabase Edge Function: sendAuctionNotification
 * Enhanced to handle comprehensive auction lifecycle notifications including:
 * - Admin notifications for all auction events
 * - Automatic winner selection and notifications
 * - Account holder winner summaries
 * Called by pg_net from the notification cron job
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Email service interfaces
interface EmailRecipient {
  email: string;
  name?: string;
}

interface AuctionClosedEmailData {
  auctionId: string;
  policyNumber: string;
  assetDisplayName: string;
  totalBids: number;
  closedAt: string;
  accountHolderName: string;
}

interface NewBidEmailData {
  auctionId: string;
  policyNumber: string;
  assetDisplayName: string;
  brokerName: string;
  bidAmount: number;
  timeRemaining: string;
  accountHolderName: string;
}

interface AdminNotificationData {
  auctionId: string;
  policyNumber: string;
  policyId: string;
  assetDisplayName: string;
  eventType: 'AUCTION_CREATED' | 'AUCTION_CLOSED' | 'WINNERS_SELECTED' | 'SYSTEM_ERROR';
  executionTimestamp: string;
  processingDuration?: number;
  totalBids: number;
  participantCount: number;
  winnerSelectionCriteria?: string;
  errorDetails?: string;
  troubleshootingInfo?: string;
  systemMetrics?: Record<string, any>;
}

interface WinnerNotificationData {
  auctionId: string;
  policyNumber: string;
  assetDisplayName: string;
  winningPosition: number;
  winningBidAmount: number;
  brokerName: string;
  accountHolderContact: {
    name: string;
    email: string;
    phone?: string;
  };
}

interface AccountHolderWinnersData {
  auctionId: string;
  policyNumber: string;
  assetDisplayName: string;
  accountHolderName: string;
  winners: Array<{
    position: number;
    brokerName: string;
    bidAmount: number;
    contact: {
      name: string;
      email: string;
      phone?: string;
    };
  }>;
}

interface BidWithBroker {
  id: string;
  auctionId: string;
  brokerId: string;
  amount: number;
  coverageDetails?: string;
  createdAt: string;
  broker: {
    id: string;
    user: {
      id: string;
      email: string;
      first_name: string;
      last_name: string;
      display_name?: string;
      phone?: string;
    };
    company_name?: string;
  };
}

interface SelectedWinner {
  bidId: string;
  brokerId: string;
  position: number;
  bidAmount: number;
  brokerName: string;
  brokerContact: {
    name: string;
    email: string;
    phone?: string;
  };
  selectionScore: number;
}

// Winner Selection Service (simplified for Edge Function)
class WinnerSelectionService {
  selectWinners(bids: BidWithBroker[]): SelectedWinner[] {
    if (bids.length === 0) return [];

    // Score each bid based on criteria
    const scoredBids = bids.map(bid => {
      const priceScore = this.calculatePriceScore(bid, bids);
      const coverageScore = this.calculateCoverageScore(bid);
      const totalScore = (priceScore * 0.7) + (coverageScore * 0.3);

      return { ...bid, totalScore };
    });

    // Sort by total score and take top 3
    const sortedBids = scoredBids
      .sort((a, b) => b.totalScore - a.totalScore)
      .slice(0, 3);

    return sortedBids.map((bid, index) => ({
      bidId: bid.id,
      brokerId: bid.brokerId,
      position: index + 1,
      bidAmount: bid.amount,
      brokerName: this.getBrokerDisplayName(bid.broker),
      brokerContact: {
        name: this.getBrokerDisplayName(bid.broker),
        email: bid.broker.user.email,
        phone: bid.broker.user.phone,
      },
      selectionScore: bid.totalScore,
    }));
  }

  private calculatePriceScore(bid: BidWithBroker, allBids: BidWithBroker[]): number {
    if (allBids.length === 1) return 100;
    const prices = allBids.map(b => b.amount);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    if (minPrice === maxPrice) return 100;
    return ((maxPrice - bid.amount) / (maxPrice - minPrice)) * 100;
  }

  private calculateCoverageScore(bid: BidWithBroker): number {
    if (!bid.coverageDetails) return 0;
    const details = bid.coverageDetails.trim();
    let score = 20; // Base score
    if (details.length > 50) score += 20;
    if (details.length > 150) score += 20;
    if (details.length > 300) score += 20;

    const qualityKeywords = ['cobertura completa', 'todo riesgo', 'responsabilidad civil'];
    const foundKeywords = qualityKeywords.filter(keyword =>
      details.toLowerCase().includes(keyword.toLowerCase())
    );
    score += Math.min(20, foundKeywords.length * 7);

    return Math.max(0, Math.min(100, score));
  }

  private getBrokerDisplayName(broker: BidWithBroker['broker']): string {
    if (broker.company_name) return broker.company_name;
    if (broker.user.display_name) return broker.user.display_name;
    return `${broker.user.first_name} ${broker.user.last_name}`.trim() || broker.user.email;
  }
}

class BrevoEmailService {
  private apiKey: string;
  private senderEmail: string;
  private senderName: string;
  private baseUrl = 'https://api.brevo.com/v3';

  constructor() {
    this.apiKey = Deno.env.get('BREVO_API_KEY') || '';
    this.senderEmail = Deno.env.get('BREVO_SENDER_EMAIL') || '<EMAIL>';
    this.senderName = Deno.env.get('BREVO_SENDER_NAME') || 'Zeeguros';

    if (!this.apiKey) {
      throw new Error('BREVO_API_KEY environment variable is required');
    }
  }

  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }

  private async sendEmail(
    to: EmailRecipient[],
    subject: string,
    htmlContent: string
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const payload = {
        sender: {
          name: this.senderName,
          email: this.senderEmail,
        },
        to: to.map(recipient => ({
          email: recipient.email,
          name: recipient.name || recipient.email,
        })),
        subject,
        htmlContent,
        textContent: this.stripHtml(htmlContent),
      };

      const response = await fetch(`${this.baseUrl}/smtp/email`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'api-key': this.apiKey,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Brevo API error: ${response.status} - ${errorData.message || 'Unknown error'}`);
      }

      const result = await response.json();
      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error) {
      console.error('Failed to send email via Brevo:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async sendAuctionClosedNotification(
    recipient: EmailRecipient,
    data: AuctionClosedEmailData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const siteUrl = Deno.env.get('NEXT_PUBLIC_SITE_URL') || 'https://zeeguros.com';
    
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #3ea050; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">Subasta Finalizada</h1>
        </div>
        
        <div style="padding: 30px; background-color: #f9f9f9;">
          <h2 style="color: #333;">Hola ${data.accountHolderName},</h2>
          
          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            Tu subasta para la póliza <strong>${data.policyNumber}</strong> ha finalizado.
          </p>
          
          <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #3ea050; margin-top: 0;">Detalles de la Subasta</h3>
            <ul style="list-style: none; padding: 0;">
              <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Activo:</strong> ${data.assetDisplayName}</li>
              <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Total de ofertas:</strong> ${data.totalBids}</li>
              <li style="padding: 8px 0;"><strong>Finalizada el:</strong> ${data.closedAt}</li>
            </ul>
          </div>
          
          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            ${data.totalBids > 0 
              ? 'Puedes revisar las ofertas recibidas y seleccionar hasta 3 ganadores en tu panel de control.'
              : 'No se recibieron ofertas para esta subasta. Puedes crear una nueva subasta si lo deseas.'
            }
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${siteUrl}/account-holder/auctions/${data.auctionId}" 
               style="background-color: #3ea050; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Ver Subasta
            </a>
          </div>
          
          <p style="font-size: 14px; color: #777; margin-top: 30px;">
            Si tienes alguna pregunta, no dudes en <NAME_EMAIL>
          </p>
        </div>
        
        <div style="background-color: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© 2025 Zeeguros. Todos los derechos reservados.</p>
        </div>
      </div>
    `;

    return this.sendEmail([recipient], `Tu subasta ha finalizado - ${data.policyNumber}`, htmlContent);
  }

  async sendNewBidNotification(
    recipient: EmailRecipient,
    data: NewBidEmailData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const siteUrl = Deno.env.get('NEXT_PUBLIC_SITE_URL') || 'https://zeeguros.com';
    
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #3ea050; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">Nueva Oferta Recibida</h1>
        </div>
        
        <div style="padding: 30px; background-color: #f9f9f9;">
          <h2 style="color: #333;">Hola ${data.accountHolderName},</h2>
          
          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            Has recibido una nueva oferta para tu subasta de la póliza <strong>${data.policyNumber}</strong>.
          </p>
          
          <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #3ea050; margin-top: 0;">Detalles de la Oferta</h3>
            <ul style="list-style: none; padding: 0;">
              <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Activo:</strong> ${data.assetDisplayName}</li>
              <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Broker:</strong> ${data.brokerName}</li>
              <li style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Oferta:</strong> €${data.bidAmount.toFixed(2)}</li>
              <li style="padding: 8px 0;"><strong>Tiempo restante:</strong> ${data.timeRemaining}</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${siteUrl}/account-holder/auctions/${data.auctionId}" 
               style="background-color: #3ea050; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Ver Subasta
            </a>
          </div>
          
          <p style="font-size: 14px; color: #777; margin-top: 30px;">
            Si tienes alguna pregunta, no dudes en <NAME_EMAIL>
          </p>
        </div>
        
        <div style="background-color: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© 2025 Zeeguros. Todos los derechos reservados.</p>
        </div>
      </div>
    `;

    return this.sendEmail([recipient], `Nueva oferta recibida - ${data.policyNumber}`, htmlContent);
  }

  async sendAdminAuctionNotification(data: AdminNotificationData): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const adminEmail = Deno.env.get('ADMIN_EMAIL') || '<EMAIL>';
    const adminDashboardUrl = Deno.env.get('ADMIN_DASHBOARD_URL') || 'https://admin.zeeguros.com';

    const eventTypeLabels = {
      'AUCTION_CREATED': 'AUCTION_CREATED',
      'AUCTION_CLOSED': 'AUCTION_CLOSED',
      'WINNERS_SELECTED': 'WINNERS_SELECTED',
      'SYSTEM_ERROR': 'SYSTEM_ERROR'
    };

    const subject = `[ZEEGUROS-ADMIN] [${eventTypeLabels[data.eventType]}] - Auction ${data.auctionId} - Policy ${data.policyNumber}`;

    const htmlContent = `
      <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 800px; margin: 0 auto; background-color: #f8f9fa;">
        <div style="background-color: #dc3545; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">ZEEGUROS ADMIN NOTIFICATION</h1>
          <h2 style="margin: 10px 0 0 0; font-size: 18px; font-weight: normal;">${eventTypeLabels[data.eventType]}</h2>
        </div>

        <div style="background-color: white; padding: 30px; border-left: 4px solid #dc3545;">
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
            <div>
              <h3 style="color: #495057; margin: 0 0 10px 0; font-size: 16px;">Auction Details</h3>
              <p style="margin: 5px 0; color: #6c757d;"><strong>Auction ID:</strong> ${data.auctionId}</p>
              <p style="margin: 5px 0; color: #6c757d;"><strong>Policy ID:</strong> ${data.policyId}</p>
              <p style="margin: 5px 0; color: #6c757d;"><strong>Policy Number:</strong> ${data.policyNumber}</p>
              <p style="margin: 5px 0; color: #6c757d;"><strong>Asset:</strong> ${data.assetDisplayName}</p>
            </div>
            <div>
              <h3 style="color: #495057; margin: 0 0 10px 0; font-size: 16px;">System Metrics</h3>
              <p style="margin: 5px 0; color: #6c757d;"><strong>Total Bids:</strong> ${data.totalBids}</p>
              <p style="margin: 5px 0; color: #6c757d;"><strong>Participants:</strong> ${data.participantCount}</p>
              <p style="margin: 5px 0; color: #6c757d;"><strong>Timestamp:</strong> ${data.executionTimestamp}</p>
              ${data.processingDuration ? `<p style="margin: 5px 0; color: #6c757d;"><strong>Duration:</strong> ${data.processingDuration}ms</p>` : ''}
            </div>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${adminDashboardUrl}/auctions/${data.auctionId}"
               style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
              View in Admin Dashboard
            </a>
          </div>
        </div>

        <div style="background-color: #6c757d; color: white; padding: 15px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">Zeeguros Admin System - Automated Notification</p>
          <p style="margin: 5px 0 0 0;">Generated at ${new Date().toISOString()}</p>
        </div>
      </div>
    `;

    return this.sendEmail([{ email: adminEmail, name: 'Zeeguros Admin' }], subject, htmlContent);
  }

  async sendWinnerNotification(recipient: EmailRecipient, data: WinnerNotificationData): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const positionLabels = { 1: '1er', 2: '2do', 3: '3er' };
    const positionLabel = positionLabels[data.winningPosition as keyof typeof positionLabels] || `${data.winningPosition}º`;
    const siteUrl = Deno.env.get('NEXT_PUBLIC_SITE_URL') || 'https://zeeguros.com';

    const subject = `¡Felicidades! Has ganado una subasta - ${data.policyNumber}`;

    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #3ea050; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">¡Felicidades!</h1>
          <h2 style="margin: 10px 0 0 0; font-weight: normal;">Has sido seleccionado como ganador</h2>
        </div>

        <div style="background-color: white; padding: 30px;">
          <div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px; margin-bottom: 25px; text-align: center;">
            <h3 style="color: #155724; margin: 0 0 10px 0;">Posición: ${positionLabel} Lugar</h3>
            <p style="color: #155724; margin: 0; font-size: 18px; font-weight: bold;">Oferta Ganadora: €${data.winningBidAmount.toFixed(2)}</p>
          </div>

          <h3 style="color: #333; margin: 0 0 15px 0;">Detalles de la Subasta</h3>
          <p style="margin: 5px 0; color: #555;"><strong>Póliza:</strong> ${data.policyNumber}</p>
          <p style="margin: 5px 0; color: #555;"><strong>Activo:</strong> ${data.assetDisplayName}</p>
          <p style="margin: 5px 0; color: #555;"><strong>Tu posición:</strong> ${positionLabel} lugar</p>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 25px 0;">
            <h3 style="color: #333; margin: 0 0 15px 0;">Información de Contacto del Titular</h3>
            <p style="margin: 5px 0; color: #555;"><strong>Nombre:</strong> ${data.accountHolderContact.name}</p>
            <p style="margin: 5px 0; color: #555;"><strong>Email:</strong> ${data.accountHolderContact.email}</p>
            ${data.accountHolderContact.phone ? `<p style="margin: 5px 0; color: #555;"><strong>Teléfono:</strong> ${data.accountHolderContact.phone}</p>` : ''}
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${siteUrl}/broker/auctions/${data.auctionId}"
               style="background-color: #3ea050; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Ver Detalles de la Subasta
            </a>
          </div>
        </div>

        <div style="background-color: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© 2025 Zeeguros. Todos los derechos reservados.</p>
        </div>
      </div>
    `;

    return this.sendEmail([recipient], subject, htmlContent);
  }

  async sendAccountHolderWinnersNotification(recipient: EmailRecipient, data: AccountHolderWinnersData): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const winnersTable = data.winners.map((winner) => `
      <tr style="border-bottom: 1px solid #dee2e6;">
        <td style="padding: 15px; text-align: center; font-weight: bold; color: #3ea050;">${winner.position}º</td>
        <td style="padding: 15px;">${winner.brokerName}</td>
        <td style="padding: 15px; text-align: right; font-weight: bold;">€${winner.bidAmount.toFixed(2)}</td>
        <td style="padding: 15px;">
          <div style="font-size: 14px;">
            <div><strong>${winner.contact.name}</strong></div>
            <div style="color: #6c757d;">${winner.contact.email}</div>
            ${winner.contact.phone ? `<div style="color: #6c757d;">${winner.contact.phone}</div>` : ''}
          </div>
        </td>
      </tr>
    `).join('');

    const siteUrl = Deno.env.get('NEXT_PUBLIC_SITE_URL') || 'https://zeeguros.com';
    const subject = `Ganadores seleccionados para tu subasta - ${data.policyNumber}`;

    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto;">
        <div style="background-color: #3ea050; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">Ganadores Seleccionados</h1>
          <h2 style="margin: 10px 0 0 0; font-weight: normal;">Tu subasta ha finalizado</h2>
        </div>

        <div style="background-color: white; padding: 30px;">
          <p style="font-size: 16px; color: #333; margin-bottom: 25px;">
            Hola ${data.accountHolderName},
          </p>

          <p style="font-size: 16px; line-height: 1.6; color: #555; margin-bottom: 25px;">
            El sistema ha seleccionado automáticamente los 3 mejores ganadores para tu subasta de <strong>${data.assetDisplayName}</strong>
            (Póliza: ${data.policyNumber}) basándose en el precio más bajo y la cobertura más completa.
          </p>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 25px 0;">
            <h3 style="color: #333; margin: 0 0 20px 0; text-align: center;">Ganadores Seleccionados</h3>
            <table style="width: 100%; border-collapse: collapse; background-color: white; border-radius: 5px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <thead>
                <tr style="background-color: #3ea050; color: white;">
                  <th style="padding: 15px; text-align: center;">Posición</th>
                  <th style="padding: 15px; text-align: left;">Broker</th>
                  <th style="padding: 15px; text-align: right;">Oferta</th>
                  <th style="padding: 15px; text-align: left;">Contacto</th>
                </tr>
              </thead>
              <tbody>
                ${winnersTable}
              </tbody>
            </table>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${siteUrl}/account-holder/auctions/${data.auctionId}"
               style="background-color: #3ea050; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Ver Detalles Completos
            </a>
          </div>
        </div>

        <div style="background-color: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">© 2025 Zeeguros. Todos los derechos reservados.</p>
        </div>
      </div>
    `;

    return this.sendEmail([recipient], subject, htmlContent);
  }
}

serve(async (req) => {
  // CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  }

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verify request method
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const { type, auctionIds } = await req.json()

    if (!type || !auctionIds || !Array.isArray(auctionIds)) {
      return new Response(
        JSON.stringify({ error: 'Invalid request body. Expected type and auctionIds array.' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Initialize services
    const emailService = new BrevoEmailService()
    const winnerSelectionService = new WinnerSelectionService()

    const results = []

    for (const auctionId of auctionIds) {
      const startTime = Date.now()

      try {
        if (type === 'auction_created') {
          // Get auction details for admin notification
          const { data: auction, error } = await supabase
            .from('auction')
            .select(`
              id,
              policy!inner(
                id,
                policy_number,
                account_holder:account_holder_id(
                  user!inner(id, email, first_name, last_name, display_name)
                ),
                asset(
                  asset_type,
                  vehicle(brand, model, year)
                )
              ),
              created_at
            `)
            .eq('id', auctionId)
            .eq('status', 'OPEN')
            .single()

          if (error || !auction) {
            console.error(`Failed to fetch auction ${auctionId}:`, error)

            // Send admin error notification
            await emailService.sendAdminAuctionNotification({
              auctionId,
              policyNumber: 'Unknown',
              policyId: 'Unknown',
              assetDisplayName: 'Unknown',
              eventType: 'SYSTEM_ERROR',
              executionTimestamp: new Date().toISOString(),
              processingDuration: Date.now() - startTime,
              totalBids: 0,
              participantCount: 0,
              errorDetails: `Failed to fetch auction for creation notification: ${error?.message || 'Unknown error'}`,
              troubleshootingInfo: 'Check auction ID and database connectivity'
            })

            results.push({ auctionId, success: false, error: 'Auction not found' })
            continue
          }

          const asset = auction.policy.asset
          const vehicle = asset?.vehicle
          const assetDisplayName = vehicle
            ? `${vehicle.brand} ${vehicle.model} (${vehicle.year})`
            : `${asset?.asset_type || 'Activo'}`

          // Send admin notification for auction created
          const adminResult = await emailService.sendAdminAuctionNotification({
            auctionId: auction.id,
            policyNumber: auction.policy.policy_number || 'N/A',
            policyId: auction.policy.id,
            assetDisplayName,
            eventType: 'AUCTION_CREATED',
            executionTimestamp: new Date().toISOString(),
            processingDuration: Date.now() - startTime,
            totalBids: 0,
            participantCount: 0,
            systemMetrics: {
              auctionCreatedAt: auction.created_at,
              policyId: auction.policy.id
            }
          })

          results.push({
            auctionId,
            success: adminResult.success,
            messageId: adminResult.messageId,
            error: adminResult.error,
            notificationType: 'admin_auction_created'
          })

        } else if (type === 'auction_closed') {
          // Get auction details with bids and broker information
          const { data: auction, error } = await supabase
            .from('auction')
            .select(`
              id,
              policy!inner(
                id,
                policy_number,
                account_holder:account_holder_id(
                  user!inner(id, email, first_name, last_name, display_name, phone)
                ),
                asset(
                  asset_type,
                  vehicle(brand, model, year)
                )
              ),
              bids:bid(
                id,
                broker_id,
                amount,
                coverage_details,
                created_at,
                broker:broker_id(
                  id,
                  company_name,
                  user!inner(id, email, first_name, last_name, display_name, phone)
                )
              ),
              updated_at
            `)
            .eq('id', auctionId)
            .eq('status', 'CLOSED')
            .single()

          if (error || !auction) {
            console.error(`Failed to fetch auction ${auctionId}:`, error)

            // Send admin error notification
            await emailService.sendAdminAuctionNotification({
              auctionId,
              policyNumber: 'Unknown',
              policyId: 'Unknown',
              assetDisplayName: 'Unknown',
              eventType: 'SYSTEM_ERROR',
              executionTimestamp: new Date().toISOString(),
              processingDuration: Date.now() - startTime,
              totalBids: 0,
              participantCount: 0,
              errorDetails: `Failed to fetch auction: ${error?.message || 'Unknown error'}`,
              troubleshootingInfo: 'Check auction ID and database connectivity'
            })

            results.push({ auctionId, success: false, error: 'Auction not found' })
            continue
          }

          const accountHolder = auction.policy.account_holder
          const user = accountHolder.user
          const asset = auction.policy.asset
          const vehicle = asset?.vehicle
          const bids = auction.bids || []

          const assetDisplayName = vehicle
            ? `${vehicle.brand} ${vehicle.model} (${vehicle.year})`
            : `${asset?.asset_type || 'Activo'}`

          // Send admin notification for auction closed
          await emailService.sendAdminAuctionNotification({
            auctionId: auction.id,
            policyNumber: auction.policy.policy_number || 'N/A',
            policyId: auction.policy.id,
            assetDisplayName,
            eventType: 'AUCTION_CLOSED',
            executionTimestamp: new Date().toISOString(),
            totalBids: bids.length,
            participantCount: new Set(bids.map(bid => bid.broker_id)).size,
            systemMetrics: {
              auctionDuration: 'N/A',
              avgBidAmount: bids.length > 0 ? bids.reduce((sum, bid) => sum + bid.amount, 0) / bids.length : 0
            }
          })

          // Send auction closed notification to account holder
          const emailData: AuctionClosedEmailData = {
            auctionId: auction.id,
            policyNumber: auction.policy.policy_number || 'N/A',
            assetDisplayName,
            totalBids: bids.length,
            closedAt: new Date(auction.updated_at).toLocaleString('es-ES'),
            accountHolderName: user.display_name || `${user.first_name} ${user.last_name}`.trim() || 'Usuario',
          }

          const emailResult = await emailService.sendAuctionClosedNotification(
            { email: user.email, name: emailData.accountHolderName },
            emailData
          )

          // If there are bids, automatically select winners
          if (bids.length > 0) {
            const winners = winnerSelectionService.selectWinners(bids)

            if (winners.length > 0) {
              console.log(`Selecting ${winners.length} winners for auction ${auctionId}`)

              // Store winners in database with proper error handling
              for (const winner of winners) {
                try {
                  const { error } = await supabase
                    .from('auction_winner')
                    .insert({
                      auction_id: auctionId,
                      broker_id: winner.brokerId,
                      bid_id: winner.bidId,
                      position: winner.position,
                      contact_data_revealed_at: null // Initially null, set when account holder contacts broker
                    })

                  if (error) {
                    console.error(`Failed to insert winner ${winner.position} for auction ${auctionId}:`, error)
                    throw error
                  }

                  console.log(`Successfully inserted winner ${winner.position} (bid: ${winner.bidId}) for auction ${auctionId}`)
                } catch (insertError) {
                  console.error(`Critical error inserting winner for auction ${auctionId}:`, insertError)
                  // Continue with other winners even if one fails
                }
              }

              // Send admin notification for winners selected
              await emailService.sendAdminAuctionNotification({
                auctionId: auction.id,
                policyNumber: auction.policy.policy_number || 'N/A',
                policyId: auction.policy.id,
                assetDisplayName,
                eventType: 'WINNERS_SELECTED',
                executionTimestamp: new Date().toISOString(),
                processingDuration: Date.now() - startTime,
                totalBids: bids.length,
                participantCount: new Set(bids.map((bid: any) => bid.broker_id)).size,
                winnerSelectionCriteria: 'Automatic selection based on lowest price (70%) and most comprehensive coverage (30%)'
              })

              // Send winner notifications to brokers
              for (const winner of winners) {
                await emailService.sendWinnerNotification(
                  { email: winner.brokerContact.email, name: winner.brokerContact.name },
                  {
                    auctionId: auction.id,
                    policyNumber: auction.policy.policy_number || 'N/A',
                    assetDisplayName,
                    winningPosition: winner.position,
                    winningBidAmount: winner.bidAmount,
                    brokerName: winner.brokerName,
                    accountHolderContact: {
                      name: emailData.accountHolderName,
                      email: user.email,
                      phone: user.phone
                    }
                  }
                )
              }

              // Send winners summary to account holder
              await emailService.sendAccountHolderWinnersNotification(
                { email: user.email, name: emailData.accountHolderName },
                {
                  auctionId: auction.id,
                  policyNumber: auction.policy.policy_number || 'N/A',
                  assetDisplayName,
                  accountHolderName: emailData.accountHolderName,
                  winners: winners.map(winner => ({
                    position: winner.position,
                    brokerName: winner.brokerName,
                    bidAmount: winner.bidAmount,
                    contact: winner.brokerContact
                  }))
                }
              )
            }
          }

          results.push({
            auctionId,
            success: emailResult.success,
            messageId: emailResult.messageId,
            error: emailResult.error,
            winnersSelected: bids.length > 0 ? winnerSelectionService.selectWinners(bids).length : 0
          })
        }
      } catch (error) {
        console.error(`Error processing auction ${auctionId}:`, error)

        // Send admin error notification
        try {
          await emailService.sendAdminAuctionNotification({
            auctionId,
            policyNumber: 'Unknown',
            policyId: 'Unknown',
            assetDisplayName: 'Unknown',
            eventType: 'SYSTEM_ERROR',
            executionTimestamp: new Date().toISOString(),
            processingDuration: Date.now() - startTime,
            totalBids: 0,
            participantCount: 0,
            errorDetails: error instanceof Error ? error.message : 'Unknown error',
            troubleshootingInfo: 'Check logs for detailed error information'
          })
        } catch (adminError) {
          console.error('Failed to send admin error notification:', adminError)
        }

        results.push({
          auctionId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        })
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        processed: results.length,
        results,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Edge function error:', error)
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})
