"use client";

import { cn } from "@/lib/utils";
import { formatCurrency } from "@/lib/utils";
import { GuaranteeType } from "@prisma/client";
import { translateGuaranteeType } from "@/features/policies/utils/translations";

// Enhanced interface to support full Coverage/BidCoverage model
interface CoverageCardProps {
  // Basic coverage info
  type: GuaranteeType;
  customName?: string | null;
  description?: string | null;

  // Limit information
  limit?: number | null;
  limitIsUnlimited?: boolean;
  limitIsFullCost?: boolean;
  limitPerDay?: number | null;
  limitMaxDays?: number | null;
  limitMaxMonths?: number | null;

  // Liability specific limits
  liabilityBodilyCap?: number | null;
  liabilityPropertyCap?: number | null;

  // Deductible information
  deductible?: number | null;
  deductiblePercent?: number | null;

  // Comparison mode
  comparisonMode?: 'better' | 'worse' | 'equal' | null;

  // Styling
  className?: string;
}

export function CoverageCard({
  type,
  customName,
  description,
  limit,
  limitIsUnlimited,
  limitIsFullCost,
  limitPerDay,
  limitMaxDays,
  limitMaxMonths,
  liabilityBodilyCap,
  liabilityPropertyCap,
  deductible,
  deductiblePercent,
  comparisonMode,
  className
}: CoverageCardProps) {

  // Get the display title
  const title = type === GuaranteeType.OTHER && customName
    ? customName
    : translateGuaranteeType(type);

  // Format limit display
  const formatLimitDisplay = (): string | null => {
    if (limitIsUnlimited) {
      return "Ilimitado";
    }

    if (limitIsFullCost) {
      return "Coste completo";
    }

    if (limitPerDay && limitMaxDays) {
      const totalLimit = Number(limitPerDay) * limitMaxDays;
      return `${formatCurrency(Number(limitPerDay))}/día (máx. ${limitMaxDays} días - Total: ${formatCurrency(totalLimit)})`;
    }

    if (limitPerDay && limitMaxMonths) {
      return `${formatCurrency(Number(limitPerDay))}/día (máx. ${limitMaxMonths} meses)`;
    }

    if (limitPerDay) {
      return `${formatCurrency(Number(limitPerDay))}/día`;
    }

    if (limit) {
      return formatCurrency(Number(limit));
    }

    // Handle liability specific caps
    if (liabilityBodilyCap || liabilityPropertyCap) {
      const parts = [];
      if (liabilityBodilyCap) {
        parts.push(`Personas: ${formatCurrency(Number(liabilityBodilyCap))}`);
      }
      if (liabilityPropertyCap) {
        parts.push(`Daños: ${formatCurrency(Number(liabilityPropertyCap))}`);
      }
      return parts.join(' | ');
    }

    return null;
  };

  // Format deductible display
  const formatDeductibleDisplay = (): string | null => {
    if (deductiblePercent && deductible) {
      return `${deductiblePercent}% (mín. ${formatCurrency(Number(deductible))})`;
    }

    if (deductiblePercent) {
      return `${deductiblePercent}%`;
    }

    if (deductible) {
      return formatCurrency(Number(deductible));
    }

    return null;
  };

  const limitDisplay = formatLimitDisplay();
  const deductibleDisplay = formatDeductibleDisplay();

  // Get comparison styling
  const getComparisonStyling = () => {
    if (!comparisonMode) return {};

    switch (comparisonMode) {
      case 'better':
        return {
          borderColor: '#10B981', // green-500
          backgroundColor: '#F0FDF4', // green-50
          indicatorColor: '#10B981'
        };
      case 'worse':
        return {
          borderColor: '#EF4444', // red-500
          backgroundColor: '#FEF2F2', // red-50
          indicatorColor: '#EF4444'
        };
      case 'equal':
        return {
          borderColor: '#6B7280', // gray-500
          backgroundColor: '#F9FAFB', // gray-50
          indicatorColor: '#6B7280'
        };
      default:
        return {};
    }
  };

  const comparisonStyling = getComparisonStyling();

  return (
    <div
      className={cn(
        "bg-white border border-gray-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow",
        className
      )}
      style={{
        borderColor: comparisonStyling.borderColor,
        backgroundColor: comparisonStyling.backgroundColor
      }}
    >
      <div className="flex items-center gap-3 mb-2">
        <div
          className="w-2 h-2 rounded-full flex-shrink-0 mt-1"
          style={{
            backgroundColor: comparisonStyling.indicatorColor || '#3AE386'
          }}
        />
        <h4 className="font-semibold text-sm text-gray-900 leading-tight">{title}</h4>
      </div>

      {limitDisplay && (
        <p className="text-gray-600 pl-4 mb-2">
          <span className="text-xs text-gray-500">Límite de cobertura: </span>
          <span className="text-xs font-semibold text-gray-900">{limitDisplay}</span>
        </p>
      )}

      {deductibleDisplay && (
        <p className="text-gray-600 pl-4 mb-2">
          <span className="text-xs text-gray-500">Franquicia: </span>
          <span className="text-xs font-semibold text-gray-900">{deductibleDisplay}</span>
        </p>
      )}

      {description && (
        <div className="text-xs text-gray-600 leading-relaxed pl-4">
          {description}
        </div>
      )}
    </div>
  );
}