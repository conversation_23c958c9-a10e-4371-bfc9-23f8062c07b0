"use client";

import React from "react";
import Link from "next/link";
import { ZCard } from "@/components/shared/z-card";
import { PrimaryButton } from "@/components/shared/primary-button";
import { Badge } from "@/components/ui/badge";
import { cn, formatCurrency } from "@/lib/utils";
import { formatTimeRemaining } from "@/features/auctions/utils/time-formatting";
import { formatInsurerCompany } from "@/lib/format-insurer";
import { Eye, Clock, Check, Medal } from "lucide-react";
import { AssetType } from "@/types/policy";

export interface AuctionSummary {
  id: string;
  identifier: string;
  endsAt: string; // ISO date string
  annualPremium: number;
  currency?: string; // Defaults to 'EUR'
  currentInsurer?: string | null;
  assetDisplayName: string;
  quotesReceived: number;
  status?: "OPEN" | "CLOSED" | "SIGNED_POLICY" | "CANCELED" | "EXPIRED";
  assetType?: AssetType | null;
}

interface AuctionSummaryCardProps {
  auction: AuctionSummary;
  className?: string;
  baseUrl?: string;
}

export function AuctionSummaryCard({ auction, className, baseUrl = "/account-holder" }: AuctionSummaryCardProps) {
  const isFinished = new Date(auction.endsAt).getTime() <= Date.now();
  const timeRemaining = formatTimeRemaining(auction.endsAt);

  const getAssetTypeIcon = (assetType: AssetType): string => {
    return "⚖️";
  };

  const statusLabelMap: Record<string, string> = {
    CLOSED: "Cerrada",
    SIGNED_POLICY: "Póliza firmada",
    CANCELED: "Cancelada",
    EXPIRED: "Expirada",
  };

  const effectiveStatus = auction.status || (isFinished ? "CLOSED" : "OPEN");
  const statusText = statusLabelMap[effectiveStatus];

  const getCtaInfo = () => {
    switch (effectiveStatus) {
      case "CLOSED":
        return { text: "Ver ganadores", icon: <Medal className="h-3.5 w-3.5" /> };
      default:
        return { text: "Ver detalles", icon: <Eye className="h-3.5 w-3.5" /> };
    }
  };

  const { text: ctaText, icon: ctaIcon } = getCtaInfo();

  const statusBadge = (
    <Badge
      variant={
        effectiveStatus === "CANCELED" || effectiveStatus === "EXPIRED"
          ? "destructive"
          : effectiveStatus === "SIGNED_POLICY"
          ? "default"
          : "outline"
      }
      className={cn("px-2 py-1 text-xs font-semibold", {
        "bg-yellow-100 text-yellow-800 border-yellow-200":
          effectiveStatus === "CLOSED",
        "bg-emerald-100 text-emerald-800 border-emerald-200":
          effectiveStatus === "OPEN",
      })}
    >
      {effectiveStatus === "OPEN" ? (
        <span className="inline-flex items-center">
          <Clock className="w-3 h-3 mr-1" /> {timeRemaining}
        </span>
      ) : (
        <span>{statusText}</span>
      )}
    </Badge>
  );

  return (
    <ZCard variant="policy" className={cn("transition-all hover:shadow-lg p-2 h-full w-full min-w-[280px]", className)}>
      <div className="space-y-2 flex flex-col h-full">
        {/* Header with Asset Icon, Title and Status Badge */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-primary/10 to-primary/20 border border-primary/20 flex items-center justify-center text-sm shadow-sm">
              {getAssetTypeIcon((auction.assetType || "CAR") as AssetType)}
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex items-center justify-between">
                <h3 className="font-bold text-sm text-foreground truncate">
                  <span className="font-mono">{auction.identifier}</span>
                </h3>
                {statusBadge}
              </div>
              <div className="text-xs text-muted-foreground truncate mt-0.5" title={auction.assetDisplayName}>
                {auction.assetDisplayName}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content - Compact Layout */}
        <div className="flex-grow space-y-1">
          {/* Compact Info Grid */}
          <div className="grid grid-cols-1 gap-1 text-xs">
            {/* Insurer and Premium in one row */}
            <div className="flex justify-between items-center">
              <div className="min-w-0 flex-1">
                <div className="font-semibold text-foreground truncate" title={auction.currentInsurer ? formatInsurerCompany(auction.currentInsurer) : "Compañía no disponible"}>
                  {auction.currentInsurer ? formatInsurerCompany(auction.currentInsurer) : "No disponible"}
                </div>
                <div className="text-muted-foreground truncate text-[10px]">
                  Aseguradora actual
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-foreground">
                  {auction.annualPremium && auction.annualPremium > 0
                    ? `${formatCurrency(auction.annualPremium)}/año`
                    : "No disponible"}
                </div>
                <div className="text-muted-foreground text-[10px]">
                  Prima actual
                </div>
              </div>
            </div>
            
            {/* Offers and Potential Savings in one row */}
            <div className="flex justify-between items-center pt-1 border-t border-border/50">
              <div className="min-w-0 flex-1">
                <div className="text-[10px] font-semibold text-muted-foreground uppercase tracking-wide">
                  Ofertas
                </div>
                <div className="font-medium text-foreground text-[11px]">
                  {auction.quotesReceived > 0 ? `${auction.quotesReceived} ofertas` : "Sin ofertas"}
                </div>
              </div>
              <div className="text-right">
                <div className="text-[10px] font-semibold text-muted-foreground uppercase tracking-wide">
                  {effectiveStatus === "OPEN" ? "Finaliza" : "Finalizada"}
                </div>
                <div className="text-muted-foreground text-[11px]">
                  {effectiveStatus === "OPEN" 
                    ? (() => {
                        // Show auction closing time for OPEN auctions
                        const endDate = new Date(auction.endsAt);
                        
                        // Format closing time
                        return endDate.toLocaleDateString('es-ES', { 
                          day: 'numeric', 
                          month: 'short',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        });
                      })()
                    : (() => {
                        // Show completion date for finished auctions
                        const endDate = new Date(auction.endsAt);
                        
                        return endDate.toLocaleDateString('es-ES', { 
                          day: 'numeric', 
                          month: 'short',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        });
                      })()
                  }
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer with Actions */}
        <div className="flex justify-end items-center pt-1 border-t border-border/30">
          <Link href={`${baseUrl}/auctions/${auction.id}`}>
            <PrimaryButton size="sm" className="h-6 text-xs gap-1 px-2">
              {ctaIcon} {ctaText}
            </PrimaryButton>
          </Link>
        </div>
      </div>
    </ZCard>
  );
}
