import { AuctionState } from "@prisma/client";

// Utility function to mask broker names
function maskBrokerName(fullName: string): string {
  if (!fullName || fullName.trim() === '') return fullName;
  
  const nameParts = fullName.trim().split(' ');
  if (nameParts.length === 1) {
    return nameParts[0] || ''; // If only one name, return as is
  }
  
  const firstName = nameParts[0] || '';
  const lastName = nameParts[nameParts.length - 1];
  const lastNameInitial = lastName ? lastName.charAt(0).toUpperCase() : '';
  
  return `${firstName} ${lastNameInitial}.`;
}

interface BidDetail {
  id: string;
  brokerName: string;
  annualPremium?: number;
  createdAt: Date | string;
}

interface TimelineEvent {
  id: string;
  title: string;
  time: string;
  status: 'completed' | 'pending';
  bidDetails?: BidDetail[]; // For grouped offers
}

interface AuctionData {
  id: string;
  status: AuctionState;
  startDate: Date;
  endDate: Date;
  createdAt: Date;
  bids: Array<{
    id: string;
    createdAt: Date;
    annualPremium?: number;
    broker: {
      user: {
        displayName?: string | null;
        firstName?: string | null;
      };
    };
  }>;
}

/**
 * Generates dynamic timeline events based on auction data
 */
export function generateAuctionTimeline(auction: AuctionData): TimelineEvent[] {
  const events: TimelineEvent[] = [];
  const now = new Date();

  // 1. Auction started event
  events.push({
    id: `auction-started-${auction.id}`,
    title: 'Subasta iniciada',
    time: formatEventTime(auction.startDate),
    status: 'completed'
  });

  // 2. Bid events - group by date to avoid timeline clutter
  const sortedBids = [...auction.bids].sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
  
  // Group bids by date (YYYY-MM-DD format)
  const bidsByDate = new Map<string, typeof sortedBids>();
  sortedBids.forEach(bid => {
    const dateKey = bid.createdAt.toISOString().split('T')[0] || ''; // YYYY-MM-DD
    if (dateKey && !bidsByDate.has(dateKey)) {
      bidsByDate.set(dateKey, []);
    }
    const existingBids = bidsByDate.get(dateKey);
    if (existingBids && dateKey) {
      existingBids.push(bid);
    }
  });

  let isFirstBidEver = true;
  
  // Process each date group
  Array.from(bidsByDate.entries())
    .sort(([dateA], [dateB]) => {
      const safeeDateA = dateA ?? '';
      const safeDateB = dateB ?? '';
      return safeeDateA.localeCompare(safeDateB);
    })
    .forEach(([dateKey, bidsOnDate]) => {
      if (bidsOnDate.length >= 3) {
        // Group multiple bids on same date
        const firstBidOfDay = bidsOnDate[0];
        const lastBidOfDay = bidsOnDate[bidsOnDate.length - 1]; // Get the latest bid
        if (firstBidOfDay && lastBidOfDay) {
          // Create bid details for the grouped entry
          const bidDetails: BidDetail[] = bidsOnDate.map(bid => {
            const brokerName = bid.broker.user.displayName || 
                              bid.broker.user.firstName || 
                              'Corredor';
            return {
              id: bid.id,
              brokerName: maskBrokerName(brokerName),
              annualPremium: bid.annualPremium,
              createdAt: bid.createdAt
            };
          });
          
          events.push({
            id: `grouped-bids-${dateKey}`,
            title: `${bidsOnDate.length} ofertas recibidas`,
            time: formatEventTime(lastBidOfDay.createdAt), // Use latest bid time
            status: 'completed',
            bidDetails
          });
          isFirstBidEver = false;
        }
      } else {
        // Show individual bids for dates with less than 3 bids
        bidsOnDate.forEach(bid => {
          const brokerName = bid.broker.user.displayName || 
                            bid.broker.user.firstName || 
                            'Corredor';
          const maskedBrokerName = maskBrokerName(brokerName);
          
          if (isFirstBidEver) {
            // First bid ever
            events.push({
              id: `first-bid-${bid.id}`,
              title: `Primera oferta recibida de ${maskedBrokerName}`,
              time: formatEventTime(bid.createdAt),
              status: 'completed'
            });
            isFirstBidEver = false;
          } else {
            // Subsequent bids
            events.push({
              id: `bid-${bid.id}`,
              title: `Nueva oferta recibida de ${maskedBrokerName}`,
              time: formatEventTime(bid.createdAt),
              status: 'completed'
            });
          }
        });
      }
    });

  // 3. Auction end event
  const isAuctionEnded = now >= auction.endDate || auction.status === 'CLOSED';
  const endTitle = auction.status === 'CLOSED' ? 'Subasta finalizada' : 'Subasta finaliza';

  events.push({
    id: `auction-end-${auction.id}`,
    title: endTitle,
    time: formatEventTime(auction.endDate),
    status: isAuctionEnded ? 'completed' : 'pending'
  });

  // 4. Additional events for closed auctions
  if (auction.status === 'CLOSED') {
    // Pending contract signing event
    events.push({
      id: `pending-contract-${auction.id}`,
      title: 'Pendiente de firma de renovación',
      time: formatEventTime(auction.endDate), // Use end date as placeholder
      status: 'pending'
    });

    // Contract signed event (future state)
    events.push({
      id: `contract-signed-${auction.id}`,
      title: 'Contrato firmado',
      time: formatEventTime(auction.endDate), // Use end date as placeholder
      status: 'pending'
    });
  }

  return events;
}



/**
 * Formats a date for timeline display
 */
function formatEventTime(date: Date): string {
  return date.toLocaleDateString("es-ES", {
    day: "numeric",
    month: "short", 
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
}