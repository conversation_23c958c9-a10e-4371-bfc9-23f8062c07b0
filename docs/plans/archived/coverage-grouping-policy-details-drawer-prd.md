# PRD — Coverage Grouping & PolicyDetailsDrawer Enhancements

**Owner:** <PERSON>o  
**Date:** 2025-08-20  
**Scope:** Enhance PolicyDetailsDrawer.tsx to display grouped coverages and support new Coverage/BidCoverage model.

---

## I. Coverage Accordion Grouping

In the sample dataset we have **130+ sample policies**, which confirms the need to **group coverages into meaningful categories**.  
The Coberturas accordion in `@/components/shared/PolicyDetailsDrawer.tsx` should be updated to render **subcategories** that map each `GuaranteeType` into one of 8 groups.

### Main Coverage Groups → Enum Mapping

1. **Civil Liability (RC)**
   - `MANDATORY_LIABILITY`
   - `VOLUNTARY_LIABILITY`
   - Liability extensions: `LIABILITY_AT_REST`, `TRAILER_LIABILITY`, `LOAD_LIABILITY`, `CARGO_LIABILITY`

---

2. **Legal Defense & Management**
   - `LEGAL_DEFENSE`
   - `LEGAL_REPRESENTATION_EXTENSION`
   - `FINES_MANAGEMENT`
   - `LICENSE_SUSPENSION`
   - `LICENSE_SUSPENSION_SUBSIDY`
   - `THIRD_PARTY_INSOLVENCY`

---

3. **Damage to the Insured Vehicle**
   - `VEHICLE_DAMAGE`
   - `FIRE`
   - `THEFT`
   - `GLASS_BREAKAGE`
   - `WEATHER_DAMAGE`
   - `COLLISION_WITH_ANIMALS`
   - `TYRE_DAMAGE`
   - `NON_STANDARD_ACCESSORIES`
   - `CHARGING_CABLE`
   - `ERROR_REFUELING`
   - `UNAUTHORIZED_USE`
   - Total-loss variants: `TOTAL_LOSS_DAMAGE`, `TOTAL_LOSS_FIRE`, `TOTAL_LOSS_THEFT`

---

4. **Travel Assistance**
   - `TRAVEL_ASSISTANCE` (generic container)
   - `TOWING_FROM_KM0`
   - `RESCUE_EXPENSES`
   - `HOTEL_EXPENSES_TRAVEL_ASSIST`
   - Related: `REPATRIATION`, `IMMOBILIZATION`

---

5. **Personal Accidents**
   - `DRIVER_ACCIDENTS`
   - `PASSENGER_ACCIDENTS`
   - `PSYCHOLOGICAL_ASSISTANCE`
   - `PET_INJURY`
   - `EXTRAORDINARY_RISKS_PERSONS`

---

6. **Replacement Vehicle / Immobilization**
   - `VEHICLE_REPLACEMENT`
   - `PARALYZATION_COMPENSATION`
   - `HANDYMAN_SERVICE`
   - *(Optionally `IMMOBILIZATION` here instead of group 4)*

---

7. **Valuation & Compensation**
   - `NEW_VALUE_COMPENSATION`
   - `GAP_COVERAGE`
   - `ADVANCE_COMPENSATION`
   - `EXTRAORDINARY_RISKS_VEHICLE`

---

8. **Complementary Services**
   - `FREE_WORKSHOP_CHOICE`
   - `PERSONAL_BELONGINGS`
   - `ASSISTIVE_EQUIPMENT_RENTAL`
   - `LOST_KEYS`
   - `REPATRIATION` *(if not under Assistance)*
   - `OTHER` *(customName only)*

**Notes:**
- `OTHER` always defaults to Complementary Services.  
- `REPATRIATION` and `IMMOBILIZATION` may float between Assistance vs. Replacement depending on insurer wording.  

---

## II. UI Enhancements in PolicyDetailsDrawer.tsx

### Requirements
1. **Grouped accordion**: Render one section per main group (1–8).  
2. **Coverage rows**: Each row shows:
   - `description` (translated/customized)
   - `limit`, `deductible`, `per-day`, or special flags (`unlimited`, `full-cost`)  
3. **Bid comparison mode**:  
   - If comparing `PolicyCoverages` vs `BidCoverages`, highlight differences:
     - Green → better coverage in bid  
     - Red → worse coverage in bid  
     - Grey → equal  
4. **Support `customName`** for `OTHER` types.  
5. **Responsiveness**: Works on desktop and mobile.  

---

## Acceptance Criteria
- [ ] Coverages in PolicyDetailsDrawer are grouped into 8 categories.  
- [ ] Each GuaranteeType is displayed under the correct group.  
- [ ] `BidCoverages` are supported with comparison UI.  
- [ ] Unlimited / full-cost / per-day limits are displayed properly.  
- [ ] `customName` shown for `OTHER`.  

---