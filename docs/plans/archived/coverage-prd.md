# PRD — Coverage Normalization & Policy vs. Offer <PERSON>mparison (MVP)

**Owner:** <PERSON><PERSON>  
**Date:** 2025-08-20  
**Scope:** Prisma schema, seeder, and backend logic to normalize coverages and enable deterministic comparisons between **policy_coverages** and **bid_coverages** without AI inference.

---

## 0. Goal

Deliver a **simple, SQL‑friendly** model to persist policy and offer (bid) coverages and allow **deterministic “is offer better?” comparisons** without calling AI. Support common patterns (single numeric caps, unlimited, per‑day × duration, mandatory liability 70M/15M, deductibles fixed + percent).

> **Currency:** The UI renders the **€** symbol. **Do not** store symbols in the database.

---

## 1. Business Rules

1) **Currency symbol:** UI-only. Store numeric values in DB.  
2) **`customName`:** Use **only** when `type = OTHER` (or when no enum match exists).  
3) **Mandatory Liability (RCO)** for **auto/motor** must always be:
   - `liabilityBodilyCap = 70,000,000` (bodily injury)  
   - `liabilityPropertyCap = 15,000,000` (property damage)  
4) **Comparison rules (deterministic):**
   - **MANDATORY_LIABILITY:** fixed 70M/15M → no difference scoring.
   - **VOLUNTARY_LIABILITY:** higher `limit` wins.
   - **LEGAL_DEFENSE:** if any record has `limitIsUnlimited=true` → wins; else higher `limit` wins.  
     _If the wording contains variants like “unlimited with company lawyer / 1,000 for free choice”, model **two rows** of `LEGAL_DEFENSE` in the same entity._
   - **TRAVEL_ASSISTANCE (by sub‑benefit as separate rows):**
     - Towing/transport → `limitIsUnlimited=true` wins.
     - Hotel → compare `(limitPerDay, then limitMaxDays/Months)`.
     - Rescue/Custody → compare `limit`.
   - **VEHICLE_DAMAGE / FIRE / THEFT (deductibles):** dominance in two axes → lower `deductible` **and** lower `deductiblePercent` (at least one strictly lower) wins.  
   - **GLASS_BREAKAGE:** `limitIsFullCost=true` wins; else higher `limit` wins.
   - **VEHICLE_REPLACEMENT:** compare `(limitPerDay, then limitMaxDays/Months)`.
   - **NEW_VALUE_COMPENSATION (MVP):** store months/years in `description` (no automatic comparison in v1).

### Additional Rules Matrix

#### A) Liability family
- **LIABILITY_AT_REST**, **TRAILER_LIABILITY**, **THIRD_PARTY_INSOLVENCY** → compare by `limit` (higher wins).

#### B) Legal / fines / license
- **LEGAL_REPRESENTATION_EXTENSION** → compare by `limit` (higher wins).  
- **FINES_MANAGEMENT**, **LICENSE_SUSPENSION** → Boolean (presence wins).  
- **LICENSE_SUSPENSION_SUBSIDY** → compare `(limitPerDay, limitMaxMonths, limitMaxDays)`.

#### C) Assistance family
- **TOWING_FROM_KM0** → presence wins.  
- **HOTEL_EXPENSES_TRAVEL_ASSIST** → compare `(limitPerDay, then limitMaxDays/Months)`.  
- **RESCUE_EXPENSES** → compare by `limit`.

#### D) Vehicle damage / property
- **TOTAL_LOSS_DAMAGE/FIRE/THEFT** → compare by `limit`, else presence.  
- **WEATHER_DAMAGE**, **TYRE_DAMAGE**, **NON_STANDARD_ACCESSORIES**, **PERSONAL_BELONGINGS**, **LOST_KEYS**, **ERROR_REFUELING**, **CHARGING_CABLE**, **UNAUTHORIZED_USE** → compare by `limit`, else presence.

#### E) Replacement / mobility
- **VEHICLE_REPLACEMENT** → compare `(limitPerDay, then limitMaxDays/Months)`.

#### F) Accidents / persons
- **DRIVER_ACCIDENTS**, **PASSENGER_ACCIDENTS** → compare `limit` per sub-row.  
- **EXTRAORDINARY_RISKS_PERSONS/VEHICLE** → compare by `limit`, else presence.  
- **PSYCHOLOGICAL_ASSISTANCE**, **ASSISTIVE_EQUIPMENT_RENTAL**, **REPATRIATION**, **MOTORCYCLE_GEAR**, **PET_INJURY** → compare by `limit`, else presence.

#### G) Value rules
- **NEW_VALUE_COMPENSATION** → MVP: compare by `limitMaxMonths`; otherwise treat as presence.

#### H) Misc / services
- **HANDYMAN_SERVICE**, **IMMOBILIZATION**, **FREE_WORKSHOP_CHOICE** → presence wins.

#### I) GAP / Cargo
- **GAP_COVERAGE** → presence wins; if capped, higher limit wins.  
- **LOAD_LIABILITY / CARGO_LIABILITY** → compare by `limit`.

#### J) OTHER
- **OTHER** → compare by `limit` if same `customName`; if different, mark `NOT_COMPARABLE`.

---

## 2. In Scope / Out of Scope

- **In:** Prisma schema changes (Coverage & BidCoverage), seed updates, backend normalization/middleware, SQL helpers for comparisons, optional DB constraint for RCO.
- **Out:** UI/UX work (beyond rendering the € symbol and existing views).

---

## 3. Prisma Schema

### 3.1 `Coverage` (policy)

```prisma
/// Stores each coverage/guarantee attached to a Policy. MVP, SQL-friendly:
/// - `customName` ONLY when `type = OTHER`.
/// - Numeric amounts only (UI adds €).
/// - Columns cover common patterns; deep edge cases -> description.
model Coverage {
  // Identification
  id       String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  policyId String @map("policy_id") @db.Uuid

  // Coverage details
  type        GuaranteeType
  customName  String?  @map("custom_name")   // only when type = OTHER
  description String?

  // Simple numeric caps
  limit       Decimal?

  // Unlimited / full-cost flags
  limitIsUnlimited Boolean  @map("limit_is_unlimited") @default(false)
  limitIsFullCost  Boolean  @map("limit_is_full_cost")  @default(false)

  // Daily allowance (e.g., 37 per day up to 3 months)
  limitPerDay    Decimal? @map("limit_per_day")
  limitMaxDays   Int?     @map("limit_max_days")
  limitMaxMonths Int?     @map("limit_max_months")

  // Liability pair (mandatory caps)
  liabilityBodilyCap    Decimal? @map("liability_bodily_cap")
  liabilityPropertyCap  Decimal? @map("liability_property_cap")

  // Deductibles (simple + “100 + 20% remainder”)
  deductible        Decimal?
  deductiblePercent Decimal? @map("deductible_percent") // 0.20 = 20%

  // Relations
  policy Policy @relation(fields: [policyId], references: [id], onDelete: Cascade)

  // Indexes
  @@index([policyId])
  @@index([policyId, type])
  @@map("policy_coverage")
  @@schema("public")
}
```

### 3.2 `BidCoverage` (offer)

> Mirrors `Coverage` so comparisons are trivial.

```prisma
model BidCoverage {
  id      String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  bidId   String @map("bid_id") @db.Uuid

  type        GuaranteeType
  customName  String?  @map("custom_name")   // only when type = OTHER
  description String?

  limit              Decimal?
  limitIsUnlimited   Boolean  @map("limit_is_unlimited") @default(false)
  limitIsFullCost    Boolean  @map("limit_is_full_cost")  @default(false)
  limitPerDay        Decimal? @map("limit_per_day")
  limitMaxDays       Int?     @map("limit_max_days")
  limitMaxMonths     Int?     @map("limit_max_months")
  liabilityBodilyCap Decimal? @map("liability_bodily_cap")
  liabilityPropertyCap Decimal? @map("liability_property_cap")
  deductible         Decimal?
  deductiblePercent  Decimal? @map("deductible_percent")

  bid Bid @relation(fields: [bidId], references: [id], onDelete: Cascade)

  @@index([bidId])
  @@index([bidId, type])
  @@map("bid_coverage")
  @@schema("public")
}
```

### 3.3 `GuaranteeType` (note)

Ensure the enum includes the additions used in documents:  
`LIABILITY_AT_REST`, `TRAILER_LIABILITY`, `THIRD_PARTY_INSOLVENCY`, `FREE_WORKSHOP_CHOICE`, `NEW_VALUE_COMPENSATION`, `HOTEL_EXPENSES_TRAVEL_ASSIST`, `RESCUE_EXPENSES`, etc.  
_(Existing enum already covers standard market guarantees.)_

---

## 4. Seeder (`seed.ts`) Changes

### 4.1 Normalizer for RCO
```ts
function normalizeMandatoryLiability<T extends Record<string, any>>(c: T): T {
  if (c?.type === 'MANDATORY_LIABILITY') {
    return { 
      ...c, 
      liabilityBodilyCap: 70000000, 
      liabilityPropertyCap: 15000000 
    };
  }
  return c;
}
```

### 4.2 Example coverage objects (shape)
- Use numeric amounts only (no €).  
- Add flags or per‑day caps where relevant.

```ts
const sampleCoverages = [
  { type: 'MANDATORY_LIABILITY', description: 'Responsabilidad civil obligatoria' },
  { type: 'VOLUNTARY_LIABILITY', description: 'Responsabilidad civil voluntaria', limit: 50000000 },
  { type: 'LEGAL_DEFENSE', description: 'Defensa jurídica ilimitada con abogado de la compañía; 1.000 € libre elección', limitIsUnlimited: true, limit: 1000 },
  { type: 'TRAVEL_ASSISTANCE', description: 'Remolque del vehículo', limitIsUnlimited: true },
  { type: 'HOTEL_EXPENSES_TRAVEL_ASSIST', description: 'Gastos de hotel', limitPerDay: 80, limitMaxDays: 10 },
  { type: 'RESCUE_EXPENSES', description: 'Gastos de rescate / custodia', limit: 180 },
  { type: 'VEHICLE_DAMAGE', description: 'Daños propios con franquicia mixta', deductible: 100, deductiblePercent: 0.20 },
  { type: 'GLASS_BREAKAGE', description: 'Sustitución de lunas', limitIsFullCost: true },
  { type: 'NEW_VALUE_COMPENSATION', description: 'Valor a nuevo 2 años / 80% en el año 3 / resto valor de mercado' },
  { type: 'LICENSE_SUSPENSION_SUBSIDY', description: 'Subsidio diario por retirada del permiso', limitPerDay: 37, limitMaxMonths: 3 },
  { type: 'OTHER', customName: 'Intereses y gastos de financiación en reparación', description: 'Cobertura de cargos financieros' }
];
```

### 4.3 Insert (policy & bid)
```ts
for (const raw of sampleCoverages) {
  const c = normalizeMandatoryLiability(raw);

  await prisma.coverage.create({
    data: {
      policyId: newPolicy.id,
      type: c.type as any,
      customName: c.customName ?? null,
      description: c.description ?? null,
      limit: c.limit ?? null,
      limitIsUnlimited: c.limitIsUnlimited ?? false,
      limitIsFullCost: c.limitIsFullCost ?? false,
      limitPerDay: c.limitPerDay ?? null,
      limitMaxDays: c.limitMaxDays ?? null,
      limitMaxMonths: c.limitMaxMonths ?? null,
      liabilityBodilyCap: c.liabilityBodilyCap ?? null,
      liabilityPropertyCap: c.liabilityPropertyCap ?? null,
      deductible: c.deductible ?? null,
      deductiblePercent: c.deductiblePercent ?? null,
    },
  });
}
```
> Apply the same shape to `prisma.bidCoverage.create(...)` when seeding bids.

---

## 5. Backend Enforcement (Prisma Middleware)

Ensure RCO is normalized on **any** create/update:

```ts
prisma.$use(async (params, next) => {
  if (params.model === 'Coverage' && (params.action === 'create' || params.action === 'update')) {
    const d = params.args?.data ?? {};
    if (d.type === 'MANDATORY_LIABILITY') {
      d.liabilityBodilyCap = 70000000;
      d.liabilityPropertyCap = 15000000;
    }
    params.args.data = d;
  }
  if (params.model === 'BidCoverage' && (params.action === 'create' || params.action === 'update')) {
    const d = params.args?.data ?? {};
    if (d.type === 'MANDATORY_LIABILITY') {
      d.liabilityBodilyCap = 70000000;
      d.liabilityPropertyCap = 15000000;
    }
    params.args.data = d;
  }
  return next(params);
});
```

> If you need to scope to auto/motor only, fetch `policyId`/`bidId` → join to policy/asset type → apply caps conditionally.

---

## 6. SQL Helpers for Comparison

### 6.1 Voluntary Liability
```sql
SELECT b.policy_id,
       CASE WHEN b.limit > p.limit THEN 'BID_BETTER'
            WHEN b.limit = p.limit THEN 'TIE'
            ELSE 'POLICY_BETTER' END AS verdict
FROM bid_coverages b
JOIN policy_coverages p 
  ON p.policy_id = b.policy_id AND p.type = 'VOLUNTARY_LIABILITY'
WHERE b.type = 'VOLUNTARY_LIABILITY';
```

### 6.2 Legal Defense (unlimited beats numeric; else higher numeric)
```sql
SELECT b.policy_id,
       CASE 
         WHEN b.unlim = 1 AND p.unlim = 0 THEN 'BID_BETTER'
         WHEN p.unlim = 1 AND b.unlim = 0 THEN 'POLICY_BETTER'
         WHEN COALESCE(b.max_limit,0) > COALESCE(p.max_limit,0) THEN 'BID_BETTER'
         WHEN COALESCE(b.max_limit,0) = COALESCE(p.max_limit,0) THEN 'TIE'
         ELSE 'POLICY_BETTER'
       END AS verdict
FROM (
  SELECT policy_id,
         MAX(CASE WHEN limit_is_unlimited THEN 1 ELSE 0 END) AS unlim,
         MAX(limit) AS max_limit
  FROM bid_coverages WHERE type='LEGAL_DEFENSE'
  GROUP BY policy_id
) b
JOIN (
  SELECT policy_id,
         MAX(CASE WHEN limit_is_unlimited THEN 1 ELSE 0 END) AS unlim,
         MAX(limit) AS max_limit
  FROM policy_coverages WHERE type='LEGAL_DEFENSE'
  GROUP BY policy_id
) p USING (policy_id);
```

### 6.3 Vehicle Replacement (€/day then days)
```sql
SELECT b.policy_id,
       CASE 
         WHEN COALESCE(b.limit_per_day,0) > COALESCE(p.limit_per_day,0) THEN 'BID_BETTER'
         WHEN COALESCE(b.limit_per_day,0) < COALESCE(p.limit_per_day,0) THEN 'POLICY_BETTER'
         WHEN COALESCE(b.limit_max_days,0) > COALESCE(p.limit_max_days,0) THEN 'BID_BETTER'
         WHEN COALESCE(b.limit_max_days,0) = COALESCE(p.limit_max_days,0) THEN 'TIE'
         ELSE 'POLICY_BETTER'
       END AS verdict
FROM bid_coverages b
JOIN policy_coverages p
  ON p.policy_id = b.policy_id AND p.type='VEHICLE_REPLACEMENT'
WHERE b.type='VEHICLE_REPLACEMENT';
```

### 6.4 Own Damage (deductible dominance)
```sql
SELECT b.policy_id,
       CASE
         WHEN (COALESCE(b.deductible, 1e12) <= COALESCE(p.deductible, 1e12))
          AND (COALESCE(b.deductible_percent, 1e12) <= COALESCE(p.deductible_percent, 1e12))
          AND ((COALESCE(b.deductible, 1e12) < COALESCE(p.deductible, 1e12))
            OR (COALESCE(b.deductible_percent, 1e12) < COALESCE(p.deductible_percent, 1e12)))
           THEN 'BID_BETTER'
         WHEN (COALESCE(p.deductible, 1e12) <= COALESCE(b.deductible, 1e12))
          AND (COALESCE(p.deductible_percent, 1e12) <= COALESCE(b.deductible_percent, 1e12))
          AND ((COALESCE(p.deductible, 1e12) < COALESCE(b.deductible, 1e12))
            OR (COALESCE(p.deductible_percent, 1e12) < COALESCE(b.deductible_percent, 1e12)))
           THEN 'POLICY_BETTER'
         ELSE 'MIXED'
       END AS verdict
FROM bid_coverages b
JOIN policy_coverages p
  ON p.policy_id = b.policy_id AND p.type='VEHICLE_DAMAGE'
WHERE b.type='VEHICLE_DAMAGE';
```

---

## 7. Acceptance Criteria

- [ ] Prisma schema compiles and migrates (`policy_coverage`, `bid_coverage` updated).  
- [ ] Seeder inserts coverages using the **new columns**; RCO normalized to 70M/15M.  
- [ ] Backend Prisma middleware enforces RCO caps on create/update for `Coverage` and `BidCoverage`.  
- [ ] Deterministic SQL comparisons return **BID_BETTER / POLICY_BETTER / TIE** for targeted coverages.  
- [ ] `customName` only present when `type = OTHER`.  
- [ ] No currency symbols persisted; UI renders them.

---

## 8. Implementation Steps

1. Apply Prisma schema changes; run `prisma generate` and migrations.  
2. Update seeder objects to the new shape (add flags / per-day fields where needed) and call the RCO normalizer.  
3. Add Prisma middleware for RCO normalization on create/update.  
4. (Optional) Add DB CHECK constraint for RCO.  
5. Implement SQL helpers (or a read model / view) for comparisons.  
6. Seed dev DB and verify with sample policies/offers.  
7. (Optional) Expose `GET /policies/:id/coverage-comparison` returning per-type verdicts.

---

## 9. Examples

### Canonical Mandatory Liability (auto/motor)
```json
{
  "type": "MANDATORY_LIABILITY",
  "description": "Compulsory liability",
  "liabilityBodilyCap": 70000000,
  "liabilityPropertyCap": 15000000
}
```

### Legal Defense (unlimited vs numeric)
- Policy rows:  
  - `{ "type": "LEGAL_DEFENSE", "limitIsUnlimited": true }`  
  - `{ "type": "LEGAL_DEFENSE", "limit": 1000 }`  
- Bid rows:  
  - `{ "type": "LEGAL_DEFENSE", "limit": 1500 }` → still **loses** vs unlimited.