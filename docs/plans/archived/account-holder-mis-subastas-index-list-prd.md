# PRD — Account Holder “Mis Subastas” (Index/List)

**Doc status:** Draft v0.1
**Owner:** BMad Orchestrator (Approver: <PERSON>con<PERSON>)
**Target release:** Next minor
**Repo:** `zee-next-app`
**Area:** `src/app/account-holder/auctions` (role: Account Holder; domain: Auctions)

---

## 1. Summary

Implement the **“Mis Subastas”** index view for Account Holders.

**It must:**

* Preserve the current **empty state** UI when there are no auctions.
* Show a **list of the user’s auctions** when present, reusing existing components and **shadcn/ui** styles.
* Display, per auction: **Subasta (identifier) · Tiempo restante · Prima anual · Aseguradora actual · Activo · Ofertas recibidas**.
* Follow constraints: **role-based routing**, **Spanish UI text**, **server-side API** for data, **TypeScript + Zod**, **shadcn/ui + Tailwind**.

**Non-goal:** viewing details (detail page is out of scope for v1).

---

## 2. Problem / Why

Account Holders need a central place to see the status of their auctions and decide next actions (open details, monitor time, track incoming quotes). Today the section exists but lacks populated list behavior.

---

## 3. Goals & Non-Goals

### Goals

* Render a **performant, accessible** list of the user’s auctions.
* Keep **empty state UX** identical to current design.
* Enforce **security**: all data via **server-side API**, no client DB calls.
* **Reuse shared UI components**; avoid introducing new primitives unnecessarily.

### Non-Goals

* Auction creation wizard (reuse existing flow at `/account-holder/policies/new-policy`) — **not part of this PRD**.
* Auction detail page.
* Quote management or winner selection.
* Admin/Broker surfaces.

---

## 4. Users & Roles

* **Primary:** Account Holder (**ROLE: ACCOUNT\_HOLDER**).
* **Permissions:** Only the authenticated account holder can view **their own** auctions.

---

## 5. Scope (In/Out)

### In scope

* `GET` listing for account holder’s auctions.
* Empty state (as-is).
* List state: **card-based layout** compatible with mobile/desktop.
* Navigation to auction detail (**link stub** to `/account-holder/auctions/[id]`).

### Out of scope (phase 1)

* Pagination/filters/sorting. Rehuse the same layout as policy list.
* **CTA “Crear subasta”** (optional enhancement if product requests it) that links to `/account-holder/policies/new-policy`.
* Any broker/admin surface.

---

## 6. UX / UI Requirements

### Page

* **Route:** `src/app/account-holder/auctions/page.tsx`
* **Title:** “Mis Subastas”
* **Subtitle:** “Gestiona tus subastas de seguros.”

### Empty state (unchanged)

* Centered card with message: **“No hay subastas disponibles.”** Matching current spacing/tone.

### List state

* **Layout:** responsive card list (1 column mobile, 2–3 columns ≥ sm/lg).
* **Each auction card shows:**

  * **Subasta:** `<identifier>` (e.g., `ZEE-AU-000123`)
  * **Tiempo restante:** countdown/badge; if finished → **“Finalizada”**
  * **Prima anual:** currency formatted (default **EUR**)
  * **Aseguradora actual:** insurer name or **“—”**
  * **Activo:** e.g., **“Seat León (2022)”**
  * **Ofertas recibidas:** integer count
* **Interactivity:** Card is clickable → navigates to detail route (stub ok).
* **Components to reuse:** `components/ui/card`, `badge`, `separator`, icons (**lucide-react**); `components/shared/countdown.tsx` if compatible.
* **Colors:** White / Black / **Lime #3AE386** / **Emerald #3EA050**.
* **Copy:** Spanish only (UI); code in English.

### Loading & Error UI

* **Loading:** “Cargando…” in a neutral card.
* **Fetch error (generic, no internals):** “No se pudo cargar la información. Inténtalo de nuevo.”

### Accessibility (A11y)

* **Color is not the sole means** of conveying state.

---

## 7. Data Contract & API

### Fetch model (UI expects)

```ts
export type AuctionSummary = {
  id: string;                // UUID/ulid
  identifier: string;        // human-friendly code
  endsAt: string;            // ISO date string
  annualPremium: number;     // base currency units (EUR cents — see notes)
  currency?: string;         // default 'EUR'
  currentInsurer?: string | null;
  assetDisplayName: string;  // e.g., "Seat León (2022)"
  quotesReceived: number;    // integer
};
```

### Endpoint

* **Method:** `GET /api/auctions?scope=account-holder`
* **Auth:** Server-side validation of **Supabase** session + role (**ACCOUNT\_HOLDER**).
* **Response 200:** `{ "auctions": AuctionSummary[] }`
* **Errors:** `401 Unauthorized`, `400 Bad Request`, `500 Internal` (generic message only).

### Validation (server)

* **Zod** for query/body validation.
* **Prisma** (singleton from `src/lib/db.ts`) for data access.
* **No client-side Supabase DB operations.**

### Performance

* `cache: "no-store"` to keep remaining time accurate.
* Target **TTFB < 500ms** for typical list (≤ 20 items).

---

## 8. Navigation

* From sidebar **“Mis Subastas”** → `/account-holder/auctions`
* Card click → `/account-holder/auctions/[id]` (detail page **not** in scope).

---

## 9. Analytics & Telemetry

*(Stub; usable once PostHog is integrated)*

* `ah.auctions_list_viewed` — on page render `{ count, hasAuctions, ts }`
* `ah.auction_card_clicked` — on card click `{ auctionId, identifier, position }`
* `ah.auctions_fetch_failed` — on non-OK fetch `{ status }`

---

## 10. Edge Cases

* Auction ended (`endsAt <= now`): badge **“Finalizada”**; **no negative countdown**.
* Unknown insurer: show **“Compañía no disponible”**.
* Zero quotes: show **Sin ofertas**.
* Missing premium: show **“Prima no disponible”**.
* **Timezone:** remaining time computed client-side from **Time zone in Madrid (GMT+2)**.
* Large lists: future pagination (not in v1).

---

## 11. Copy (Spanish UI Strings)

* **Page title:** “Mis Subastas”
* **Subtitle:** “Gestiona tus subastas de seguros.”
* **Empty:** “No hay subastas disponibles.”

**Labels:**

* Subasta
* Tiempo restante
* Prima anual
* Aseguradora actual
* Activo
* Ofertas recibidas

**Badges & States:**

* Ended badge: “Finalizada”

**System messages:**

* Loading: “Cargando…”
* Error: “No se pudo cargar la información. Inténtalo de nuevo.”

---

## 12. Dependencies

* **Supabase** session validation on server.
* **Prisma** models for Auctions (read-only for this view).
* **shadcn/ui**, **lucide-react**.
* Optional: `components/shared/countdown.tsx`.

---

## 13. Risks & Mitigations

* **Time skew for countdown** → compute from `endsAt` vs `Date.now()`; prefer coarse labels (d/h/m).
* **Unauthorized access** → strict server check by user id + role.
* **Performance with many auctions** → limit to \~20 items; add pagination later.

---

## 14. Acceptance Criteria (testable)

1. **Empty state** — With 0 auctions, `/account-holder/auctions` shows title/subtitle and “No hay subastas disponibles.” (no list grid).
2. **List state** — With ≥1 auction, each card shows identifier, Tiempo restante (**Xd Yh**, **Xh Ym**, or **Xm**; if past → “Finalizada”), Prima anual (**EUR**), Aseguradora actual, Activo, Ofertas recibidas; clicking card navigates to `/account-holder/auctions/[id]`.
3. **Security** — API without valid session returns **401**; client bundle contains **no DB keys** or direct Supabase DB calls.
4. **Errors** — On non-OK API, UI shows generic error text; no internal details leaked.
5. **A11y** — Cards tabbable; link has descriptive `aria-label`; countdown announcements are polite.

---

## 15. Implementation Notes (engineering)

* **Server Component** page; data fetched via `/api/auctions?scope=account-holder` with `no-store`.
* Types/services under `src/features/auctions/*`.
* Reuse `components/shared/countdown.tsx` if suitable; otherwise minimal util with d/h/m formatting.
* Currency: `Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR' })`.
* **Security**: validate Supabase session and role on the server; never expose DB access in client.

---

## 17. Rollout

* **Phase 1:** Empty + List states, server API read, navigation to detail stub.
* **Phase 2 (optional):** CTA “Crear subasta”, pagination/sort, skeletons, PostHog events.

---

## 18. Open Questions / Decisions

* **Currency base & units:** EUR; values are **in cents** (store/serve as integer cents). ✅
* **Filters/sorting for v1:** **AuctionState** enum filtering (deferred; not in v1 UI). ✅
* **Status badge display:** Yes; show status based on the enum of Auctionstatus.
