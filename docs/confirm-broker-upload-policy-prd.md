# PRD — UI “Confirmar firma + Subir nueva póliza” (Account Holder)

**Enfoque:** Solo UI (componentes, estados, textos, interacciones y responsive).  
**Stack UI esperado:** Next.js + TypeScript + Tailwind + shadcn/ui + lucide-react.

---

## 1) Pantallas y navegación

1) **Vista de Subasta (estado: Cerrada/Exitosa)**  
   - Tabs: **Detalles** | **Ofertas Recibidas** (persisten al refrescar).  
   - Header con 4 KPIs.

2) **Flujo modal en 3 pasos** (se dispara desde “Ofertas Recibidas”):  
   1. **Confirmar Firma** → CTA: “Sí, he firmado”.  
   2. **Sube tu Nueva Póliza** → dropzone + validaciones UI.  
   3. **Confirmar y Finalizar** → resumen + CTA final.

3) **Pantalla final: Proceso Completado**  
   - KPIs de resultado + tarjetas comparativas.

---

## 2) Componentes (props mínimas, variantes y textos)

### 2.1. HeaderKPIs
- **Props:** `bestOfferAmount`, `potentialSavings`, `statusLabel`, `timeLeftLabel`  
- **Contenido/ejemplo:**  
  - “**Mejor Oferta**: 495 €”  
  - “**Ahorro Potencial**: 105 €”  
  - “**Estado**: Exitosa” (badge)  
  - “**Tiempo Restante**: 2d 14h” (en cerrada puede decir “Finalizada”)
- **Iconos sugeridos:** `Trophy`, `PiggyBank`, `BadgeCheck`, `Clock`

### 2.2. AlertBanner (éxito)
- **Texto:** “**¡Subasta Finalizada!** Revisa las **3 mejores ofertas** en la pestaña **‘Ofertas Recibidas’** y selecciona tu favorita.”

### 2.3. Tabs
- **Labels:** “**Detalles**” | “**Ofertas Recibidas**”.  
- **Comportamiento:** indicador verde en tab activa; recuerda la última selección del usuario.

### 2.4. DetallesTab
- **PolicySummaryCard**  
  - Título: “**Póliza Actual**”  
  - Subtítulo: “**Póliza: POL-CAR-2024-007**”  
  - Botón: “**Ver detalles**”
- **WhatHappensNowCard** (lista con checks verdes):  
  - “**Subasta finalizada**. Tu subasta ha concluido exitosamente. Hemos seleccionado las 3 mejores ofertas para ti.”  
  - “**Contacta con los agentes**. Puedes contactar directamente con los corredores ganadores para resolver dudas y negociar detalles.”  
  - “**Compara las ofertas**. Revisa y compara cada propuesta frente a tu póliza actual para tomar la mejor decisión.”  
  - “**Selecciona tu favorita**. Elige la oferta que más te convenga y procede con la contratación.”  
  - “**Confirma la firma**. Una vez firmada tu nueva póliza, confírmalo en el sistema y sube tus documentos.”
- **TimelineCard**  
  - Ítems con fecha/hora (dot + label):  
    “**Subasta iniciada**”, “**Primera oferta recibida**”, “**Nueva oferta recibida**”, “**Subasta finaliza** (15 Ago 2025, 23:59)”.

### 2.5. OffersTab
- **OffersGrid** (3 columnas desktop, 1–2 en móvil/tablet).  
- **OfferCard** (por aseguradora):
  - Badges superiores:  
    - `Mejor Oferta` / `Segunda Mejor` / `Tercera Mejor`  
    - `Recomendada` (opcional)
  - **Título (aseguradora)**, **Agente**  
  - **Prima Anual** (ej. “520.00 €”)  
  - **Pill ahorro** (ej. “Ahorras 80 € (13.3%)”)  
  - **Coberturas:** “8 coberturas incluidas”  
  - **CTA principal:** “**Seleccionar Mejor Oferta**” / “**Seleccionar esta Oferta**”  
  - **Acciones secundarias:** “Comparar” • “Contactar”
- **Estados visuales OfferCard:** `default`, `hover`, `selected` (borde/ring verde + sombra), `disabled` (si otra ya está seleccionada antes de finalizar; opcional).

### 2.6. Modal Paso 1 — Confirmar Firma
- **Título:** “**Confirmar Firma de Póliza**”  
- **Resumen (panel verde):**  
  “**Agente:** María” • “**Aseguradora:** Mapfre” • “**Prima Anual:** 520.00 €” • “**Ahorro:** 80.00 €”
- **Pregunta:** “**¿Has firmado la póliza con este agente?**”  
- **CTAs:** Primario “**Sí, he firmado**” | Secundario “Cancelar”.  
- **UX:** al confirmar, cierra y abre Paso 2.

### 2.7. Modal Paso 2 — Sube tu Nueva Póliza
- **Título:** “**Confirmar Firma de Póliza**”  
- **Subtítulo:** “**Sube tu Nueva Póliza**”  
- **Texto ayuda:** “**Formatos soportados: PDF, JPG, PNG (máx. 10MB)**”
- **Dropzone** (ícono `Upload`):  
  - Estado `empty`: “**Haz clic para subir tu póliza o arrastra el archivo aquí**”  
  - Estado `dragOver`: borde punteado acentuado  
  - Estado `uploading`: barra de progreso + `%`  
  - Estado `success`: nombre de archivo + `CheckCircle2`  
  - Estado `error`: mensaje inline + `AlertCircle`
- **CTAs:** Secundario “**Volver**” | Primario “**Continuar**” (solo activo en `success`).

### 2.8. Modal Paso 3 — Confirmar y Finalizar
- **Título:** “**Confirmar Firma de Póliza**”  
- **Subtítulo:** “**Confirmar y Finalizar** — Revisa los detalles antes de finalizar el proceso.”
- **Resumen:**  
  - “**Agente Seleccionado:** María – Mapfre”  
  - “**Archivo Subido:** renfe-ticket.pdf” (con `FileText`)
- **CTAs:** Primario “**Confirmar y Finalizar**” | Secundario “**Volver**”  
- **Feedback:** toast de éxito “**Póliza guardada en tu wallet**”.

### 2.9. Pantalla — Proceso Completado
- **SuccessBanner:** “**¡Proceso Completado!** Tu nueva póliza ha sido confirmada y está activa.”
- **KPIs (4 cards):**  
  - “**Ahorro anual:** 180.00 €” (`PiggyBank`)  
  - “**Nueva aseguradora:** Generali” (`Building2`)  
  - “**Tu agente:** Pedro S.” (`UserRound`)  
  - “**Vencimiento:** 14/10/2025” (`Calendar`)
- **Dos tarjetas principales:**  
  - **Póliza Anterior**: Aseguradora AXA, Prima ~~690.00 €~~ (tachado)  
  - **Nueva Póliza**: Aseguradora Generali, Prima 510.00 €, “**7 incluidas**”, pill “**Nueva Póliza**”
- **Card vínculo a wallet:** “**Póliza: POL-MOTO-2024-004**” + botón “**Ver detalles**”
- **Footer CTAs:**  
  - Primario: “**Calificar Experiencia**”  
  - Secundario: “**Contactar Agente**”

---

## 3) Estados, feedback y micro-interacciones

- **Loaders/Skeletons:** Header KPIs, OfferCard, Timeline y Cards de póliza.  
- **Toasts:** éxito (verde) y error (rojo) para acciones clave; duración 3–4s.  
- **Botones:** `disabled` con spinner al procesar; evitar múltiples pulsaciones.  
- **Validaciones UI de archivo:** tipo no admitido, >10MB, cancelado.  
- **Focus management en modales:** trampa de foco + retorno al trigger al cerrar.  
- **Atajos teclado en modales:** `Esc` cierra, `Enter` confirma cuando el primario está habilitado.

---

## 4) Responsive

- **Breakpoints:**  
  - **Mobile (<640px):** KPIs en grid 2×2, OffersGrid stack 1 por fila, modales fullscreen, CTA principal sticky en OfferCard.  
  - **Tablet (≥640px y <1024px):** grid 2 columnas para ofertas.  
  - **Desktop (≥1024px):** 3 columnas como en mockups.

---

## 5) Accesibilidad (mínimo)

- Contraste AA en textos/badges.  
- Targets táctiles ≥44×44px.  
- Labels y `aria-label` claros en botones: “Seleccionar esta oferta — Mapfre”.  
- Lectura de estado de subida con `aria-live="polite"`.

---

## 6) Entregables UI

- **Componentes (archivos sugeridos):**  
  - `HeaderKPIs.tsx`  
  - `AlertBanner.tsx`  
  - `Tabs.tsx`  
  - `PolicySummaryCard.tsx`  
  - `WhatHappensNowCard.tsx`  
  - `TimelineCard.tsx`  
  - `OfferCard.tsx` + `OffersGrid.tsx`  
  - `ConfirmSignatureModal.tsx`  
  - `UploadPolicyModal.tsx`  
  - `FinalizeModal.tsx`  
  - `ProcessCompletedView.tsx`
- **Props clave** (solo UI): handlers tipo `onSelect`, `onConfirm`, `onUploadChange`, `onFinalize`; los datos se inyectan por props.

---

## 7) Criterios de aceptación (UI)

- El usuario **ve los 4 KPIs**, el **banner** y puede **cambiar de tab** sin perder estado.  
- Cada **OfferCard** muestra badges, prima, ahorro, coberturas y CTAs.  
- Al pulsar **Seleccionar…** se abre **Modal Paso 1**; el flujo avanza a **Paso 2** y **Paso 3** sin saltos.  
- En **Paso 2**, la **dropzone** refleja estados `empty/drag/uploading/success/error`.  
- Tras **Confirmar y Finalizar**, se muestra la **pantalla de Proceso Completado** con KPIs y tarjetas como en los diseños.  
- **Responsive** correcto en móvil/tablet/desktop; accesibilidad básica OK.

---

### Iconos lucide recomendados
`Trophy`, `PiggyBank`, `BadgeCheck`, `Clock`, `Upload`, `CheckCircle2`, `AlertCircle`, `FileText`, `Building2`, `UserRound`, `Calendar`.
